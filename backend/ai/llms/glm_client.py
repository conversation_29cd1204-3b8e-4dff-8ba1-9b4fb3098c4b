# Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
# SPDX-License-Identifier: MIT

import os
import json
import logging
import asyncio
from typing import Dict, List, Any, Optional
import httpx
from datetime import datetime

logger = logging.getLogger(__name__)

class GLMClient:
    """GLM-4-Flash API客户端，用于新闻影响分析"""
    
    def __init__(self):
        self.api_key = os.getenv('GLM_API_KEY')
        self.base_url = "https://open.bigmodel.cn/api/paas/v4/chat/completions"
        self.model = "glm-4-flash"
        
        if not self.api_key:
            logger.warning("GLM_API_KEY环境变量未设置，新闻影响分析功能将不可用")
            
    def is_available(self) -> bool:
        """检查GLM API是否可用"""
        return bool(self.api_key)
    
    async def analyze_news_impact(self, news_title: str, news_content: str) -> Dict[str, Any]:
        """
        分析新闻对金融市场的影响
        
        Args:
            news_title: 新闻标题
            news_content: 新闻内容
            
        Returns:
            分析结果字典
        """
        if not self.is_available():
            return {
                'success': False,
                'error': 'GLM API不可用，请检查GLM_API_KEY环境变量'
            }
        
        try:
            # 构建分析提示词
            prompt = self._build_analysis_prompt(news_title, news_content)
            
            # 调用GLM API
            response = await self._call_glm_api(prompt)
            
            if response.get('success'):
                # 解析分析结果
                analysis_result = self._parse_analysis_result(response['content'])
                return {
                    'success': True,
                    'analysis': analysis_result,
                    'timestamp': datetime.now().isoformat()
                }
            else:
                return {
                    'success': False,
                    'error': response.get('error', '分析失败')
                }
                
        except Exception as e:
            logger.error(f"新闻影响分析失败: {e}")
            return {
                'success': False,
                'error': f'分析过程中发生错误: {str(e)}'
            }
    
    def _build_analysis_prompt(self, title: str, content: str) -> str:
        """构建分析提示词"""
        prompt = f"""
你是一位专业的金融分析师，请分析以下新闻对金融市场的潜在影响。

请参考以下分析示例，学习如何从新闻事件推导到相关股票的专业逻辑：

【示例1：供应链紧缺事件】
事件描述：Asahi旭化成向部分客户发出供应调整通知，因AI算力需求激增导致PSPI需求爆发，现有产能无法匹配市场扩张节奏，宣布将收紧其PIMEL系列感光材料的供应。
影响逻辑：感光材料供应紧缺→下游封测厂商寻找替代供应商→国内同类产品厂商受益于进口替代和供需缺口
相关股票：阳谷华泰（感光材料生产商，直接受益于供应缺口）
推荐理由：公司是国内少数具备PSPI感光材料生产能力的企业，在上游供应紧张时期将获得更多市场份额和定价权

【示例2：地缘政治事件】
事件描述：以色列对伊朗进行空袭，打击军事基地及核设施
影响逻辑：地缘冲突升级→军工需求增加+石油供应担忧→相关防务和能源股受益
相关股票：科力股份（军工设备）、洲际油气（石油开采）、金牛化工（军工材料）、金瑞矿业（军工金属材料）
推荐理由：地缘紧张推动军费支出和防务需求，同时中东冲突影响油价，相关产业链公司将从需求增长和价格上涨中受益

【示例3：安全技术事件】
事件描述：黎巴嫩多地发生手持寻呼机爆炸事件，造成人员伤亡
影响逻辑：通信安全事件→对通信设备安全性要求提升→专业通信设备厂商受益
相关股票：海能达（专业无线通信设备制造商）
推荐理由：事件突显专业通信设备的重要性，海能达作为国内领先的专网通信解决方案提供商，将受益于安全通信需求增长

【示例4：行业展会事件】
事件描述：新凯来参加上海国际半导体展，半导体设备亮相
影响逻辑：半导体展会展示新技术→行业关注度提升→相关设备和材料厂商获得关注
相关股票：新莱应材（半导体材料）、至纯科技（半导体设备）、同惠电子（测试设备）
推荐理由：展会为行业发展风向标，参展公司技术展示将提升市场预期，相关产业链公司受益于行业景气度提升

【示例5：体育产业事件】
事件描述：江苏省城市足球联赛揭幕，场均观众达8798人，相关话题播放量破亿
影响逻辑：体育赛事火爆→体育产业需求增长→体育设施和服务提供商受益
相关股票：金陵体育（体育器材设备）、共创草坪（运动场地材料）
推荐理由：足球联赛的成功举办将带动体育基础设施建设需求，相关设备和材料供应商将直接受益于体育产业发展

现在请分析以下新闻，参考上述示例的分析逻辑，重点关注事件与股票之间的因果关系：

新闻标题：{title}
新闻内容：{content}

请从以下维度进行分析，并以JSON格式返回结果：

{{
    "overall_impact": {{
        "level": "高/中/低",
        "summary": "整体影响概述"
    }},
    "us_market": {{
        "impact_level": "高/中/低",
        "direction": "利多/利空/中性",
        "affected_sectors": ["相关行业1", "相关行业2"],
        "recommended_stocks": [
            {{
                "symbol": "股票代码1",
                "name": "公司名称1",
                "impact": "利多/利空",
                "reason": "详细的因果关系分析，参考示例说明该新闻事件如何通过产业链影响到这家公司的具体业务"
            }},
            {{
                "symbol": "股票代码2",
                "name": "公司名称2",
                "impact": "利多/利空",
                "reason": "详细的因果关系分析，参考示例说明该新闻事件如何通过产业链影响到这家公司的具体业务"
            }},
            {{
                "symbol": "股票代码3",
                "name": "公司名称3",
                "impact": "利多/利空",
                "reason": "详细的因果关系分析，参考示例说明该新闻事件如何通过产业链影响到这家公司的具体业务"
            }},
            {{
                "symbol": "股票代码4",
                "name": "公司名称4",
                "impact": "利多/利空",
                "reason": "详细的因果关系分析，参考示例说明该新闻事件如何通过产业链影响到这家公司的具体业务"
            }},
            {{
                "symbol": "股票代码5",
                "name": "公司名称5",
                "impact": "利多/利空",
                "reason": "详细的因果关系分析，参考示例说明该新闻事件如何通过产业链影响到这家公司的具体业务"
            }}
        ],
        "analysis": "详细分析"
    }},
    "a_share_market": {{
        "impact_level": "高/中/低",
        "direction": "利多/利空/中性",
        "affected_sectors": ["相关行业1", "相关行业2"],
        "recommended_stocks": [
            {{
                "symbol": "股票代码1",
                "name": "公司名称1",
                "impact": "利多/利空",
                "reason": "详细的因果关系分析，参考示例说明该新闻事件如何通过产业链影响到这家公司的具体业务"
            }},
            {{
                "symbol": "股票代码2",
                "name": "公司名称2",
                "impact": "利多/利空",
                "reason": "详细的因果关系分析，参考示例说明该新闻事件如何通过产业链影响到这家公司的具体业务"
            }},
            {{
                "symbol": "股票代码3",
                "name": "公司名称3",
                "impact": "利多/利空",
                "reason": "详细的因果关系分析，参考示例说明该新闻事件如何通过产业链影响到这家公司的具体业务"
            }},
            {{
                "symbol": "股票代码4",
                "name": "公司名称4",
                "impact": "利多/利空",
                "reason": "详细的因果关系分析，参考示例说明该新闻事件如何通过产业链影响到这家公司的具体业务"
            }},
            {{
                "symbol": "股票代码5",
                "name": "公司名称5",
                "impact": "利多/利空",
                "reason": "详细的因果关系分析，参考示例说明该新闻事件如何通过产业链影响到这家公司的具体业务"
            }}
        ],
        "analysis": "详细分析"
    }},
    "hk_market": {{
        "impact_level": "高/中/低",
        "direction": "利多/利空/中性",
        "affected_sectors": ["相关行业1", "相关行业2"],
        "recommended_stocks": [
            {{
                "symbol": "股票代码1",
                "name": "公司名称1",
                "impact": "利多/利空",
                "reason": "详细的因果关系分析，参考示例说明该新闻事件如何通过产业链影响到这家公司的具体业务"
            }},
            {{
                "symbol": "股票代码2",
                "name": "公司名称2",
                "impact": "利多/利空",
                "reason": "详细的因果关系分析，参考示例说明该新闻事件如何通过产业链影响到这家公司的具体业务"
            }},
            {{
                "symbol": "股票代码3",
                "name": "公司名称3",
                "impact": "利多/利空",
                "reason": "详细的因果关系分析，参考示例说明该新闻事件如何通过产业链影响到这家公司的具体业务"
            }},
            {{
                "symbol": "股票代码4",
                "name": "公司名称4",
                "impact": "利多/利空",
                "reason": "详细的因果关系分析，参考示例说明该新闻事件如何通过产业链影响到这家公司的具体业务"
            }},
            {{
                "symbol": "股票代码5",
                "name": "公司名称5",
                "impact": "利多/利空",
                "reason": "详细的因果关系分析，参考示例说明该新闻事件如何通过产业链影响到这家公司的具体业务"
            }}
        ],
        "analysis": "详细分析"
    }},
    "major_indices": {{
        "sp500": {{"impact": "利多/利空/中性", "reason": "影响原因"}},
        "nasdaq": {{"impact": "利多/利空/中性", "reason": "影响原因"}},
        "shanghai_composite": {{"impact": "利多/利空/中性", "reason": "影响原因"}},
        "shenzhen_component": {{"impact": "利多/利空/中性", "reason": "影响原因"}},
        "hang_seng": {{"impact": "利多/利空/中性", "reason": "影响原因"}}
    }},
    "key_stocks": [
        {{
            "symbol": "股票代码1",
            "name": "公司名称1",
            "market": "美股/A股/港股",
            "impact": "利多/利空",
            "impact_level": "高/中/低",
            "reason": "详细的因果关系分析，参考示例逻辑说明该新闻事件对公司业务的具体影响路径"
        }},
        {{
            "symbol": "股票代码2",
            "name": "公司名称2",
            "market": "美股/A股/港股",
            "impact": "利多/利空",
            "impact_level": "高/中/低",
            "reason": "详细的因果关系分析，参考示例逻辑说明该新闻事件对公司业务的具体影响路径"
        }},
        {{
            "symbol": "股票代码3",
            "name": "公司名称3",
            "market": "美股/A股/港股",
            "impact": "利多/利空",
            "impact_level": "高/中/低",
            "reason": "详细的因果关系分析，参考示例逻辑说明该新闻事件对公司业务的具体影响路径"
        }}
    ],
    "risk_assessment": {{
        "short_term_risk": "高/中/低",
        "medium_term_risk": "高/中/低",
        "key_risk_factors": ["风险因素1", "风险因素2"]
    }},
    "investment_advice": {{
        "strategy": "建议的投资策略",
        "attention_points": ["注意事项1", "注意事项2"],
        "time_horizon": "短期/中期/长期"
    }}
}}

分析要求：
1. 请严格参考上述示例的分析逻辑，重点关注"事件→影响机制→具体公司业务"的因果关系链条
2. 每个市场（美股、A股、港股）必须推荐5个相关股票，按照与新闻事件的相关性从高到低排序
3. 在推荐股票时，必须清楚说明新闻事件是如何具体影响到该公司的业务和盈利能力的
4. 优先推荐与新闻事件有直接业务关联的公司，避免过于宽泛的行业关联
5. 每个股票推荐的reason字段必须包含具体的影响机制，而不是泛泛而谈
6. 确保推荐的股票代码和公司名称准确无误，优先选择知名度高、流动性好的股票

请确保返回的是有效的JSON格式，不要包含任何其他文本。
"""
        return prompt
    
    async def _call_glm_api(self, prompt: str) -> Dict[str, Any]:
        """调用GLM API"""
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
        
        payload = {
            "model": self.model,
            "messages": [
                {
                    "role": "user",
                    "content": prompt
                }
            ],
            "temperature": 0.3,
            "max_tokens": 4000,
            "stream": False
        }
        
        try:
            async with httpx.AsyncClient(timeout=60.0) as client:
                response = await client.post(
                    self.base_url,
                    headers=headers,
                    json=payload
                )
                
                if response.status_code == 200:
                    result = response.json()
                    content = result['choices'][0]['message']['content']
                    return {
                        'success': True,
                        'content': content
                    }
                else:
                    logger.error(f"GLM API调用失败: {response.status_code} - {response.text}")
                    return {
                        'success': False,
                        'error': f'API调用失败: {response.status_code}'
                    }
                    
        except Exception as e:
            logger.error(f"GLM API调用异常: {e}")
            return {
                'success': False,
                'error': f'API调用异常: {str(e)}'
            }
    
    def _parse_analysis_result(self, content: str) -> Dict[str, Any]:
        """解析分析结果"""
        try:
            # 尝试直接解析JSON
            return json.loads(content)
        except json.JSONDecodeError:
            # 如果直接解析失败，尝试提取JSON部分
            try:
                # 查找JSON开始和结束位置
                start_idx = content.find('{')
                end_idx = content.rfind('}') + 1
                
                if start_idx != -1 and end_idx > start_idx:
                    json_str = content[start_idx:end_idx]
                    return json.loads(json_str)
                else:
                    raise ValueError("无法找到有效的JSON内容")
                    
            except Exception as e:
                logger.error(f"解析分析结果失败: {e}")
                # 返回默认结构
                return {
                    "overall_impact": {
                        "level": "中",
                        "summary": "分析结果解析失败，请重试"
                    },
                    "error": f"结果解析失败: {str(e)}",
                    "raw_content": content
                }

# 全局GLM客户端实例
_glm_client = None

def get_glm_client() -> GLMClient:
    """获取GLM客户端实例"""
    global _glm_client
    if _glm_client is None:
        _glm_client = GLMClient()
    return _glm_client 