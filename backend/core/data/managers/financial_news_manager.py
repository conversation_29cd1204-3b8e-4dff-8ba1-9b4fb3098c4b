#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
财经新闻数据库管理器
负责财经新闻数据的持久化存储、查询和管理
"""

import sqlite3
import hashlib
import logging
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Tuple, Any
import json
import os
from contextlib import contextmanager

logger = logging.getLogger(__name__)

class FinancialNewsManager:
    """财经新闻数据库管理器"""
    
    def __init__(self, db_path: str = "data/financial_news.db"):
        """
        初始化财经新闻管理器
        
        Args:
            db_path: 数据库文件路径
        """
        self.db_path = db_path
        self.ensure_db_directory()
        self.init_database()
    
    def ensure_db_directory(self):
        """确保数据库目录存在"""
        db_dir = os.path.dirname(self.db_path)
        if db_dir and not os.path.exists(db_dir):
            os.makedirs(db_dir, exist_ok=True)
            logger.info(f"创建数据库目录: {db_dir}")
    
    @contextmanager
    def get_connection(self):
        """获取数据库连接的上下文管理器"""
        conn = None
        try:
            conn = sqlite3.connect(self.db_path)
            conn.row_factory = sqlite3.Row  # 使结果可以按列名访问
            yield conn
        except Exception as e:
            if conn:
                conn.rollback()
            logger.error(f"数据库操作失败: {e}")
            raise
        finally:
            if conn:
                conn.close()
    
    def init_database(self):
        """初始化数据库表结构"""
        try:
            with self.get_connection() as conn:
                # 创建财经新闻表
                conn.execute("""
                    CREATE TABLE IF NOT EXISTS financial_news (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        news_id VARCHAR(255) UNIQUE NOT NULL,
                        title TEXT NOT NULL,
                        content TEXT,
                        publish_time DATETIME NOT NULL,
                        source VARCHAR(100) NOT NULL,
                        source_name VARCHAR(100) NOT NULL,
                        url TEXT,
                        category VARCHAR(100),
                        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                        is_active BOOLEAN DEFAULT 1
                    )
                """)
                
                # 创建数据源状态表
                conn.execute("""
                    CREATE TABLE IF NOT EXISTS news_source_status (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        source VARCHAR(100) NOT NULL,
                        last_update DATETIME,
                        last_success DATETIME,
                        total_count INTEGER DEFAULT 0,
                        status VARCHAR(50) DEFAULT 'active',
                        error_message TEXT,
                        response_time FLOAT,
                        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
                    )
                """)
                
                # 创建索引
                conn.execute("CREATE INDEX IF NOT EXISTS idx_financial_news_publish_time ON financial_news(publish_time DESC)")
                conn.execute("CREATE INDEX IF NOT EXISTS idx_financial_news_source ON financial_news(source)")
                conn.execute("CREATE INDEX IF NOT EXISTS idx_financial_news_created_at ON financial_news(created_at DESC)")
                conn.execute("CREATE UNIQUE INDEX IF NOT EXISTS idx_financial_news_id ON financial_news(news_id)")
                conn.execute("CREATE UNIQUE INDEX IF NOT EXISTS idx_news_source_status_source ON news_source_status(source)")
                
                conn.commit()
                logger.info("数据库表结构初始化完成")
                
        except Exception as e:
            logger.error(f"数据库初始化失败: {e}")
            raise
    
    def generate_news_id(self, title: str, publish_time: str, url: str = "") -> str:
        """
        生成新闻唯一标识
        
        Args:
            title: 新闻标题
            publish_time: 发布时间
            url: 新闻链接
            
        Returns:
            新闻唯一标识
        """
        # 使用标题+发布时间+URL生成hash
        content = f"{title}_{publish_time}_{url}"
        return hashlib.md5(content.encode('utf-8')).hexdigest()
    
    def save_news_batch(self, news_list: List[Dict[str, Any]], source: str, source_name: str) -> Tuple[int, int]:
        """
        批量保存新闻数据
        
        Args:
            news_list: 新闻数据列表
            source: 数据源标识
            source_name: 数据源显示名称
            
        Returns:
            (新增数量, 更新数量)
        """
        if not news_list:
            return 0, 0
        
        inserted_count = 0
        updated_count = 0
        
        try:
            with self.get_connection() as conn:
                for news_item in news_list:
                    # 生成新闻ID
                    news_id = self.generate_news_id(
                        news_item.get('title', ''),
                        news_item.get('publish_time', ''),
                        news_item.get('url', '')
                    )
                    
                    # 检查是否已存在
                    existing = conn.execute(
                        "SELECT id FROM financial_news WHERE news_id = ?",
                        (news_id,)
                    ).fetchone()
                    
                    if existing:
                        # 更新现有记录
                        conn.execute("""
                            UPDATE financial_news 
                            SET title = ?, content = ?, publish_time = ?, 
                                source = ?, source_name = ?, url = ?, 
                                category = ?, updated_at = CURRENT_TIMESTAMP
                            WHERE news_id = ?
                        """, (
                            news_item.get('title', ''),
                            news_item.get('content', ''),
                            news_item.get('publish_time', ''),
                            source,
                            source_name,
                            news_item.get('url', ''),
                            news_item.get('category', ''),
                            news_id
                        ))
                        updated_count += 1
                    else:
                        # 插入新记录
                        conn.execute("""
                            INSERT INTO financial_news 
                            (news_id, title, content, publish_time, source, source_name, url, category)
                            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                        """, (
                            news_id,
                            news_item.get('title', ''),
                            news_item.get('content', ''),
                            news_item.get('publish_time', ''),
                            source,
                            source_name,
                            news_item.get('url', ''),
                            news_item.get('category', '')
                        ))
                        inserted_count += 1
                
                conn.commit()
                logger.info(f"批量保存新闻完成 - 新增: {inserted_count}, 更新: {updated_count}")
                
        except Exception as e:
            logger.error(f"批量保存新闻失败: {e}")
            raise
        
        return inserted_count, updated_count
    
    def get_news_by_source(self, source: str, limit: int = 50, offset: int = 0) -> List[Dict[str, Any]]:
        """
        按数据源查询新闻
        
        Args:
            source: 数据源标识
            limit: 限制数量
            offset: 偏移量
            
        Returns:
            新闻数据列表
        """
        try:
            with self.get_connection() as conn:
                cursor = conn.execute("""
                    SELECT * FROM financial_news 
                    WHERE source = ? AND is_active = 1
                    ORDER BY publish_time DESC
                    LIMIT ? OFFSET ?
                """, (source, limit, offset))
                
                return [dict(row) for row in cursor.fetchall()]
                
        except Exception as e:
            logger.error(f"按数据源查询新闻失败: {e}")
            return []
    
    def get_news_by_id(self, news_id: int) -> Optional[Dict[str, Any]]:
        """
        根据ID获取新闻
        
        Args:
            news_id: 新闻ID
            
        Returns:
            新闻数据字典，如果不存在返回None
        """
        try:
            with self.get_connection() as conn:
                cursor = conn.execute(
                    "SELECT * FROM financial_news WHERE id = ? AND is_active = 1",
                    (news_id,)
                )
                row = cursor.fetchone()
                return dict(row) if row else None
                
        except Exception as e:
            logger.error(f"根据ID获取新闻失败: {e}")
            return None
    
    def get_latest_news(self, limit: int = 100, sources: Optional[List[str]] = None) -> List[Dict[str, Any]]:
        """
        获取最新新闻
        
        Args:
            limit: 限制数量
            sources: 指定数据源列表，None表示所有源
            
        Returns:
            新闻数据列表
        """
        try:
            with self.get_connection() as conn:
                if sources:
                    placeholders = ','.join(['?' for _ in sources])
                    query = f"""
                        SELECT * FROM financial_news 
                        WHERE source IN ({placeholders}) AND is_active = 1
                        ORDER BY publish_time DESC
                        LIMIT ?
                    """
                    params = sources + [limit]
                else:
                    query = """
                        SELECT * FROM financial_news 
                        WHERE is_active = 1
                        ORDER BY publish_time DESC
                        LIMIT ?
                    """
                    params = [limit]
                
                cursor = conn.execute(query, params)
                return [dict(row) for row in cursor.fetchall()]
                
        except Exception as e:
            logger.error(f"获取最新新闻失败: {e}")
            return []
    
    def get_news_by_timerange(self, start_time: datetime, end_time: datetime, 
                             sources: Optional[List[str]] = None, 
                             limit: int = 200) -> List[Dict[str, Any]]:
        """
        按时间范围查询新闻
        
        Args:
            start_time: 开始时间
            end_time: 结束时间
            sources: 指定数据源列表
            limit: 限制数量
            
        Returns:
            新闻数据列表
        """
        try:
            with self.get_connection() as conn:
                if sources:
                    placeholders = ','.join(['?' for _ in sources])
                    query = f"""
                        SELECT * FROM financial_news 
                        WHERE publish_time BETWEEN ? AND ? 
                        AND source IN ({placeholders}) AND is_active = 1
                        ORDER BY publish_time DESC
                        LIMIT ?
                    """
                    params = [start_time.isoformat(), end_time.isoformat()] + sources + [limit]
                else:
                    query = """
                        SELECT * FROM financial_news 
                        WHERE publish_time BETWEEN ? AND ? AND is_active = 1
                        ORDER BY publish_time DESC
                        LIMIT ?
                    """
                    params = [start_time.isoformat(), end_time.isoformat(), limit]
                
                cursor = conn.execute(query, params)
                return [dict(row) for row in cursor.fetchall()]
                
        except Exception as e:
            logger.error(f"按时间范围查询新闻失败: {e}")
            return []
    
    def search_news(self, keyword: str, sources: Optional[List[str]] = None, 
                   limit: int = 100) -> List[Dict[str, Any]]:
        """
        搜索新闻
        
        Args:
            keyword: 搜索关键词
            sources: 指定数据源列表
            limit: 限制数量
            
        Returns:
            新闻数据列表
        """
        try:
            with self.get_connection() as conn:
                keyword_pattern = f"%{keyword}%"
                
                if sources:
                    placeholders = ','.join(['?' for _ in sources])
                    query = f"""
                        SELECT * FROM financial_news 
                        WHERE (title LIKE ? OR content LIKE ?) 
                        AND source IN ({placeholders}) AND is_active = 1
                        ORDER BY publish_time DESC
                        LIMIT ?
                    """
                    params = [keyword_pattern, keyword_pattern] + sources + [limit]
                else:
                    query = """
                        SELECT * FROM financial_news 
                        WHERE (title LIKE ? OR content LIKE ?) AND is_active = 1
                        ORDER BY publish_time DESC
                        LIMIT ?
                    """
                    params = [keyword_pattern, keyword_pattern, limit]
                
                cursor = conn.execute(query, params)
                return [dict(row) for row in cursor.fetchall()]
                
        except Exception as e:
            logger.error(f"搜索新闻失败: {e}")
            return []
    
    def update_source_status(self, source: str, status: str = 'active', 
                           error_message: str = None, response_time: float = None):
        """
        更新数据源状态
        
        Args:
            source: 数据源标识
            status: 状态
            error_message: 错误信息
            response_time: 响应时间
        """
        try:
            with self.get_connection() as conn:
                # 获取该数据源的新闻总数
                total_count = conn.execute(
                    "SELECT COUNT(*) as count FROM financial_news WHERE source = ? AND is_active = 1",
                    (source,)
                ).fetchone()['count']
                
                # 检查是否已存在记录
                existing = conn.execute(
                    "SELECT id FROM news_source_status WHERE source = ?",
                    (source,)
                ).fetchone()
                
                current_time = datetime.now().isoformat()
                
                if existing:
                    # 更新现有记录
                    update_fields = [
                        "last_update = ?",
                        "total_count = ?",
                        "status = ?",
                        "updated_at = ?"
                    ]
                    params = [current_time, total_count, status, current_time]
                    
                    if status == 'active':
                        update_fields.append("last_success = ?")
                        params.append(current_time)
                    
                    if error_message is not None:
                        update_fields.append("error_message = ?")
                        params.append(error_message)
                    
                    if response_time is not None:
                        update_fields.append("response_time = ?")
                        params.append(response_time)
                    
                    params.append(source)
                    
                    conn.execute(f"""
                        UPDATE news_source_status 
                        SET {', '.join(update_fields)}
                        WHERE source = ?
                    """, params)
                else:
                    # 插入新记录
                    conn.execute("""
                        INSERT INTO news_source_status 
                        (source, last_update, last_success, total_count, status, error_message, response_time)
                        VALUES (?, ?, ?, ?, ?, ?, ?)
                    """, (
                        source, current_time, 
                        current_time if status == 'active' else None,
                        total_count, status, error_message, response_time
                    ))
                
                conn.commit()
                logger.info(f"更新数据源状态: {source} -> {status}")
                
        except Exception as e:
            logger.error(f"更新数据源状态失败: {e}")
            raise
    
    def get_source_status(self, source: str = None) -> List[Dict[str, Any]]:
        """
        获取数据源状态
        
        Args:
            source: 指定数据源，None表示所有源
            
        Returns:
            数据源状态列表
        """
        try:
            with self.get_connection() as conn:
                if source:
                    cursor = conn.execute(
                        "SELECT * FROM news_source_status WHERE source = ?",
                        (source,)
                    )
                else:
                    cursor = conn.execute("SELECT * FROM news_source_status ORDER BY last_update DESC")
                
                return [dict(row) for row in cursor.fetchall()]
                
        except Exception as e:
            logger.error(f"获取数据源状态失败: {e}")
            return []
    
    def cleanup_old_news(self, days_to_keep: int = 30) -> int:
        """
        清理过期新闻数据
        
        Args:
            days_to_keep: 保留天数
            
        Returns:
            清理的记录数
        """
        try:
            cutoff_date = datetime.now() - timedelta(days=days_to_keep)
            
            with self.get_connection() as conn:
                cursor = conn.execute("""
                    UPDATE financial_news 
                    SET is_active = 0, updated_at = CURRENT_TIMESTAMP
                    WHERE publish_time < ? AND is_active = 1
                """, (cutoff_date.isoformat(),))
                
                deleted_count = cursor.rowcount
                conn.commit()
                
                logger.info(f"清理过期新闻完成，标记删除 {deleted_count} 条记录")
                return deleted_count
                
        except Exception as e:
            logger.error(f"清理过期新闻失败: {e}")
            return 0
    
    def get_statistics(self) -> Dict[str, Any]:
        """
        获取新闻数据统计信息
        
        Returns:
            统计信息字典
        """
        try:
            with self.get_connection() as conn:
                # 总新闻数
                total_news = conn.execute(
                    "SELECT COUNT(*) as count FROM financial_news WHERE is_active = 1"
                ).fetchone()['count']
                
                # 按数据源统计
                source_stats = conn.execute("""
                    SELECT source, source_name, COUNT(*) as count,
                           MAX(publish_time) as latest_time
                    FROM financial_news 
                    WHERE is_active = 1
                    GROUP BY source, source_name
                    ORDER BY count DESC
                """).fetchall()
                
                # 最近24小时新闻数
                yesterday = (datetime.now() - timedelta(days=1)).isoformat()
                recent_news = conn.execute(
                    "SELECT COUNT(*) as count FROM financial_news WHERE publish_time > ? AND is_active = 1",
                    (yesterday,)
                ).fetchone()['count']
                
                return {
                    'total_news': total_news,
                    'recent_24h': recent_news,
                    'source_statistics': [dict(row) for row in source_stats],
                    'last_updated': datetime.now().isoformat()
                }
                
        except Exception as e:
            logger.error(f"获取统计信息失败: {e}")
            return {}
    
    def deduplicate_news(self) -> int:
        """
        去除重复新闻数据
        
        Returns:
            去重的记录数
        """
        try:
            with self.get_connection() as conn:
                # 查找重复的新闻（基于标题和发布时间的相似性）
                cursor = conn.execute("""
                    SELECT news_id, COUNT(*) as count
                    FROM financial_news 
                    WHERE is_active = 1
                    GROUP BY news_id
                    HAVING count > 1
                """)
                
                duplicates = cursor.fetchall()
                removed_count = 0
                
                for duplicate in duplicates:
                    news_id = duplicate['news_id']
                    # 保留最新的记录，删除其他的
                    conn.execute("""
                        UPDATE financial_news 
                        SET is_active = 0, updated_at = CURRENT_TIMESTAMP
                        WHERE news_id = ? AND id NOT IN (
                            SELECT id FROM financial_news 
                            WHERE news_id = ? AND is_active = 1
                            ORDER BY created_at DESC 
                            LIMIT 1
                        )
                    """, (news_id, news_id))
                    
                    removed_count += cursor.rowcount
                
                conn.commit()
                logger.info(f"去重完成，移除 {removed_count} 条重复记录")
                return removed_count
                
        except Exception as e:
            logger.error(f"去重操作失败: {e}")
            return 0


# 全局实例
_financial_news_manager = None

def get_financial_news_manager(db_path: str = "data/financial_news.db") -> FinancialNewsManager:
    """获取财经新闻管理器实例（单例模式）"""
    global _financial_news_manager
    if _financial_news_manager is None:
        _financial_news_manager = FinancialNewsManager(db_path)
    return _financial_news_manager


if __name__ == "__main__":
    # 测试代码
    manager = FinancialNewsManager("test_financial_news.db")
    
    # 测试保存新闻
    test_news = [
        {
            'title': '测试新闻1',
            'content': '这是一条测试新闻内容',
            'publish_time': '2025-06-13 12:00:00',
            'url': 'https://test.com/news1',
            'category': '财经快讯'
        }
    ]
    
    inserted, updated = manager.save_news_batch(test_news, 'test_source', '测试数据源')
    print(f"保存结果: 新增 {inserted}, 更新 {updated}")
    
    # 测试查询
    latest_news = manager.get_latest_news(10)
    print(f"最新新闻数量: {len(latest_news)}")
    
    # 测试统计
    stats = manager.get_statistics()
    print(f"统计信息: {stats}")