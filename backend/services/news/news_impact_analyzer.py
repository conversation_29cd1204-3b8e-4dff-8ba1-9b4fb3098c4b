# Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
# SPDX-License-Identifier: MIT

import sqlite3
import json
import logging
import hashlib
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
from contextlib import contextmanager

from backend.ai.llms.glm_client import get_glm_client

logger = logging.getLogger(__name__)

class NewsImpactAnalyzer:
    """新闻影响分析管理器"""
    
    def __init__(self, db_path: str = "data/news_impact_analysis.db"):
        self.db_path = db_path
        self.glm_client = get_glm_client()
        self.init_database()
    
    @contextmanager
    def get_connection(self):
        """获取数据库连接"""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row
        try:
            yield conn
        finally:
            conn.close()
    
    def init_database(self):
        """初始化数据库表结构"""
        try:
            with self.get_connection() as conn:
                # 创建新闻影响分析表
                conn.execute("""
                    CREATE TABLE IF NOT EXISTS news_impact_analysis (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        news_hash VARCHAR(64) UNIQUE NOT NULL,
                        news_title TEXT NOT NULL,
                        news_content TEXT,
                        news_source VARCHAR(100),
                        news_publish_time DATETIME,
                        analysis_result TEXT NOT NULL,
                        overall_impact_level VARCHAR(10),
                        us_market_impact VARCHAR(10),
                        a_share_impact VARCHAR(10),
                        hk_market_impact VARCHAR(10),
                        analysis_status VARCHAR(20) DEFAULT 'completed',
                        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
                    )
                """)
                
                # 创建分析缓存表
                conn.execute("""
                    CREATE TABLE IF NOT EXISTS analysis_cache (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        cache_key VARCHAR(64) UNIQUE NOT NULL,
                        cache_data TEXT NOT NULL,
                        expires_at DATETIME NOT NULL,
                        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                    )
                """)
                
                # 创建索引
                conn.execute("CREATE INDEX IF NOT EXISTS idx_news_impact_hash ON news_impact_analysis(news_hash)")
                conn.execute("CREATE INDEX IF NOT EXISTS idx_news_impact_created ON news_impact_analysis(created_at DESC)")
                conn.execute("CREATE INDEX IF NOT EXISTS idx_analysis_cache_key ON analysis_cache(cache_key)")
                conn.execute("CREATE INDEX IF NOT EXISTS idx_analysis_cache_expires ON analysis_cache(expires_at)")
                
                conn.commit()
                logger.info("新闻影响分析数据库初始化完成")
                
        except Exception as e:
            logger.error(f"数据库初始化失败: {e}")
            raise
    
    def _generate_news_hash(self, title: str, content: str) -> str:
        """生成新闻内容的哈希值"""
        content_str = f"{title}|{content}"
        return hashlib.sha256(content_str.encode('utf-8')).hexdigest()
    
    async def analyze_news(self, news_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        分析单条新闻的市场影响
        
        Args:
            news_data: 新闻数据，包含title, content, source等字段
            
        Returns:
            分析结果
        """
        try:
            title = news_data.get('title', '')
            content = news_data.get('content', '')
            source = news_data.get('source', '')
            publish_time = news_data.get('publish_time', '')
            
            if not title or not content:
                return {
                    'success': False,
                    'error': '新闻标题或内容为空'
                }
            
            # 生成新闻哈希
            news_hash = self._generate_news_hash(title, content)
            
            # 检查是否已经分析过
            existing_analysis = self._get_existing_analysis(news_hash)
            if existing_analysis:
                logger.info(f"使用已有分析结果: {news_hash}")
                return {
                    'success': True,
                    'analysis': json.loads(existing_analysis['analysis_result']),
                    'cached': True,
                    'analysis_id': existing_analysis['id']
                }
            
            # 调用GLM进行分析,使用完整新闻
            logger.info(f"开始分析新闻: {title[:50]}...")
            analysis_result = await self.glm_client.analyze_news_impact(title, content)
            
            if not analysis_result.get('success'):
                return analysis_result
            
            # 保存分析结果
            analysis_id = self._save_analysis_result(
                news_hash, title, content, source, publish_time, analysis_result['analysis']
            )
            
            return {
                'success': True,
                'analysis': analysis_result['analysis'],
                'cached': False,
                'analysis_id': analysis_id
            }
            
        except Exception as e:
            logger.error(f"新闻分析失败: {e}")
            return {
                'success': False,
                'error': f'分析过程中发生错误: {str(e)}'
            }
    
    def _get_existing_analysis(self, news_hash: str) -> Optional[Dict[str, Any]]:
        """获取已有的分析结果"""
        try:
            with self.get_connection() as conn:
                cursor = conn.execute(
                    "SELECT * FROM news_impact_analysis WHERE news_hash = ? AND analysis_status = 'completed'",
                    (news_hash,)
                )
                row = cursor.fetchone()
                return dict(row) if row else None
        except Exception as e:
            logger.error(f"查询已有分析结果失败: {e}")
            return None
    
    def _save_analysis_result(self, news_hash: str, title: str, content: str, 
                            source: str, publish_time: str, analysis: Dict[str, Any]) -> int:
        """保存分析结果到数据库"""
        try:
            with self.get_connection() as conn:
                # 提取关键指标
                overall_impact = analysis.get('overall_impact', {}).get('level', '中')
                us_impact = analysis.get('us_market', {}).get('impact_level', '中')
                a_share_impact = analysis.get('a_share_market', {}).get('impact_level', '中')
                hk_impact = analysis.get('hk_market', {}).get('impact_level', '中')
                
                cursor = conn.execute("""
                    INSERT OR REPLACE INTO news_impact_analysis 
                    (news_hash, news_title, news_content, news_source, news_publish_time,
                     analysis_result, overall_impact_level, us_market_impact, 
                     a_share_impact, hk_market_impact, analysis_status, updated_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'completed', CURRENT_TIMESTAMP)
                """, (
                    news_hash, title, content, source, publish_time,
                    json.dumps(analysis, ensure_ascii=False),
                    overall_impact, us_impact, a_share_impact, hk_impact
                ))
                
                conn.commit()
                return cursor.lastrowid
                
        except Exception as e:
            logger.error(f"保存分析结果失败: {e}")
            raise
    
    def get_analysis_by_id(self, analysis_id: int) -> Optional[Dict[str, Any]]:
        """根据ID获取分析结果"""
        try:
            with self.get_connection() as conn:
                cursor = conn.execute(
                    "SELECT * FROM news_impact_analysis WHERE id = ?",
                    (analysis_id,)
                )
                row = cursor.fetchone()
                if row:
                    result = dict(row)
                    result['analysis_result'] = json.loads(result['analysis_result'])
                    return result
                return None
        except Exception as e:
            logger.error(f"获取分析结果失败: {e}")
            return None
    
    def get_recent_analyses(self, limit: int = 20, impact_level: Optional[str] = None) -> List[Dict[str, Any]]:
        """获取最近的分析结果"""
        try:
            with self.get_connection() as conn:
                query = """
                    SELECT id, news_title, news_source, news_publish_time,
                           overall_impact_level, us_market_impact, a_share_impact, hk_market_impact,
                           created_at
                    FROM news_impact_analysis 
                    WHERE analysis_status = 'completed'
                """
                params = []
                
                if impact_level:
                    query += " AND overall_impact_level = ?"
                    params.append(impact_level)
                
                query += " ORDER BY created_at DESC LIMIT ?"
                params.append(limit)
                
                cursor = conn.execute(query, params)
                rows = cursor.fetchall()
                
                return [dict(row) for row in rows]
                
        except Exception as e:
            logger.error(f"获取最近分析结果失败: {e}")
            return []
    
    def get_analysis_statistics(self) -> Dict[str, Any]:
        """获取分析统计信息"""
        try:
            with self.get_connection() as conn:
                # 总分析数量
                cursor = conn.execute("SELECT COUNT(*) as total FROM news_impact_analysis WHERE analysis_status = 'completed'")
                total = cursor.fetchone()['total']
                
                # 按影响级别统计
                cursor = conn.execute("""
                    SELECT overall_impact_level, COUNT(*) as count 
                    FROM news_impact_analysis 
                    WHERE analysis_status = 'completed'
                    GROUP BY overall_impact_level
                """)
                impact_stats = {row['overall_impact_level']: row['count'] for row in cursor.fetchall()}
                
                # 今日分析数量
                cursor = conn.execute("""
                    SELECT COUNT(*) as today_count 
                    FROM news_impact_analysis 
                    WHERE analysis_status = 'completed' 
                    AND DATE(created_at) = DATE('now')
                """)
                today_count = cursor.fetchone()['today_count']
                
                return {
                    'total_analyses': total,
                    'today_analyses': today_count,
                    'impact_distribution': impact_stats,
                    'last_updated': datetime.now().isoformat()
                }
                
        except Exception as e:
            logger.error(f"获取分析统计失败: {e}")
            return {
                'total_analyses': 0,
                'today_analyses': 0,
                'impact_distribution': {},
                'error': str(e)
            }
    
    async def batch_analyze_news(self, news_list: List[Dict[str, Any]], 
                               max_concurrent: int = 3) -> List[Dict[str, Any]]:
        """
        批量分析新闻
        
        Args:
            news_list: 新闻列表
            max_concurrent: 最大并发数
            
        Returns:
            分析结果列表
        """
        import asyncio
        
        results = []
        semaphore = asyncio.Semaphore(max_concurrent)
        
        async def analyze_single_news(news_data):
            async with semaphore:
                return await self.analyze_news(news_data)
        
        # 创建任务列表
        tasks = [analyze_single_news(news) for news in news_list]
        
        # 并发执行
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 处理异常结果
        processed_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                processed_results.append({
                    'success': False,
                    'error': f'分析异常: {str(result)}',
                    'news_index': i
                })
            else:
                processed_results.append(result)
        
        return processed_results
    
    def cleanup_old_analyses(self, days: int = 30) -> int:
        """清理旧的分析记录"""
        try:
            with self.get_connection() as conn:
                cursor = conn.execute("""
                    DELETE FROM news_impact_analysis 
                    WHERE created_at < datetime('now', '-{} days')
                """.format(days))
                
                deleted_count = cursor.rowcount
                conn.commit()
                
                logger.info(f"清理了 {deleted_count} 条旧分析记录")
                return deleted_count
                
        except Exception as e:
            logger.error(f"清理旧分析记录失败: {e}")
            return 0

# 全局分析器实例
_news_impact_analyzer = None

def get_news_impact_analyzer() -> NewsImpactAnalyzer:
    """获取新闻影响分析器实例"""
    global _news_impact_analyzer
    if _news_impact_analyzer is None:
        _news_impact_analyzer = NewsImpactAnalyzer()
    return _news_impact_analyzer 