'use client';

import React, { useState, useEffect } from 'react';
import LoadingSpinner from './LoadingSpinner';
import ErrorDisplay from './ErrorDisplay';
import html2canvas from 'html2canvas';
import jsPDF from 'jspdf';
import { Button } from './ui/button';
import { Download } from 'lucide-react';
import { applyPdfCompatibleStyles, applyTemporaryPdfStyles } from '../utils/pdfStyleCompat';

interface NewsImpactAnalysisProps {
  newsData: {
    id?: number;
    title: string;
    content: string;
    source?: string;
    publish_time?: string;
  };
  onClose?: () => void;
  className?: string;
}

interface AnalysisResult {
  overall_impact: {
    level: string;
    summary: string;
  };
  us_market: {
    impact_level: string;
    direction: string;
    affected_sectors: string[];
    recommended_stocks: Array<{
      symbol: string;
      name: string;
      impact: string;
      reason: string;
    }>;
    analysis: string;
  };
  a_share_market: {
    impact_level: string;
    direction: string;
    affected_sectors: string[];
    recommended_stocks: Array<{
      symbol: string;
      name: string;
      impact: string;
      reason: string;
    }>;
    analysis: string;
  };
  hk_market: {
    impact_level: string;
    direction: string;
    affected_sectors: string[];
    recommended_stocks: Array<{
      symbol: string;
      name: string;
      impact: string;
      reason: string;
    }>;
    analysis: string;
  };
  major_indices: {
    sp500: { impact: string; reason: string };
    nasdaq: { impact: string; reason: string };
    shanghai_composite: { impact: string; reason: string };
    shenzhen_component: { impact: string; reason: string };
    hang_seng: { impact: string; reason: string };
  };
  key_stocks: Array<{
    symbol: string;
    name: string;
    market: string;
    impact: string;
    impact_level: string;
    reason: string;
  }>;
  risk_assessment: {
    short_term_risk: string;
    medium_term_risk: string;
    key_risk_factors: string[];
  };
  investment_advice: {
    strategy: string;
    attention_points: string[];
    time_horizon: string;
  };
}

const NewsImpactAnalysis: React.FC<NewsImpactAnalysisProps> = ({
  newsData,
  onClose,
  className = ''
}) => {
  const [analysis, setAnalysis] = useState<AnalysisResult | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [cached, setCached] = useState(false);
  const [isExporting, setIsExporting] = useState(false);

  useEffect(() => {
    if (newsData) {
      analyzeNews();
    }
  }, [newsData]);

  const handleExportPDF = async () => {
    if (!analysis) return;
    
    setIsExporting(true);
    
    // Apply temporary PDF-compatible styles to document
    const cleanupGlobalStyles = applyTemporaryPdfStyles();
    
    try {
      // 获取要导出的元素
      const element = document.getElementById('news-analysis-content');
      if (!element) {
        throw new Error('找不到要导出的内容');
      }

      // Apply PDF-compatible styles to the target element
      const cleanupElementStyles = applyPdfCompatibleStyles(element, {
        enableColorConversion: true,
        enableFontFallbacks: true,
        enableLayoutOptimization: true
      });

      // 暂时隐藏导出按钮等不需要的元素
      const exportButton = document.querySelector('[data-export-button]');
      if (exportButton) {
        (exportButton as HTMLElement).style.display = 'none';
      }

      // 显示PDF Header用于导出
      const pdfHeader = element.querySelector('.pdf-header');
      if (pdfHeader) {
        (pdfHeader as HTMLElement).style.display = 'block';
      }

      // 使用html2canvas生成图片，增强兼容性配置
      const canvas = await html2canvas(element, {
        scale: 2, // 提高图片质量
        useCORS: true,
        allowTaint: true,
        backgroundColor: '#ffffff',
        width: element.scrollWidth,
        height: element.scrollHeight,
        scrollX: 0,
        scrollY: 0,
        ignoreElements: (element) => {
          // 忽略可能包含不兼容样式的元素
          return element.hasAttribute('data-export-button');
        },
        onclone: (clonedDoc) => {
          // 在克隆的文档中也显示PDF Header
          const clonedHeader = clonedDoc.querySelector('.pdf-header');
          if (clonedHeader) {
            (clonedHeader as HTMLElement).style.display = 'block';
          }
          
          // 在克隆文档中应用PDF兼容样式
          const clonedElement = clonedDoc.querySelector('#news-analysis-content');
          if (clonedElement instanceof HTMLElement) {
            applyPdfCompatibleStyles(clonedElement);
          }
        }
      });

      // 恢复元素显示状态
      if (exportButton) {
        (exportButton as HTMLElement).style.display = '';
      }
      if (pdfHeader) {
        (pdfHeader as HTMLElement).style.display = 'none';
      }
      
      // Cleanup applied styles
      cleanupElementStyles();
      cleanupGlobalStyles();

      // 创建PDF
      const imgData = canvas.toDataURL('image/png');
      const pdf = new jsPDF({
        orientation: 'portrait',
        unit: 'mm',
        format: 'a4'
      });

      // 计算图片在PDF中的尺寸
      const pdfWidth = pdf.internal.pageSize.getWidth();
      const pdfHeight = pdf.internal.pageSize.getHeight();
      const imgWidth = canvas.width;
      const imgHeight = canvas.height;
      
      // 计算缩放比例，保持宽高比
      const ratio = Math.min(pdfWidth / imgWidth, pdfHeight / imgHeight);
      const scaledWidth = imgWidth * ratio;
      const scaledHeight = imgHeight * ratio;
      
      // 居中放置
      const x = (pdfWidth - scaledWidth) / 2;
      const y = 10; // 距离顶部10mm

      // 如果内容高度超过一页，需要分页处理
      if (scaledHeight > pdfHeight - 20) {
        // 分页处理
        const pageHeight = pdfHeight - 20;
        const totalPages = Math.ceil(scaledHeight / pageHeight);
        
        for (let i = 0; i < totalPages; i++) {
          if (i > 0) {
            pdf.addPage();
          }
          
          const srcY = (i * pageHeight) / ratio;
          const srcHeight = Math.min(pageHeight / ratio, imgHeight - srcY);
          const destHeight = srcHeight * ratio;
          
          // 创建页面片段的canvas
          const pageCanvas = document.createElement('canvas');
          const pageCtx = pageCanvas.getContext('2d');
          pageCanvas.width = imgWidth;
          pageCanvas.height = srcHeight;
          
          pageCtx?.drawImage(canvas, 0, -srcY);
          const pageImgData = pageCanvas.toDataURL('image/png');
          
          pdf.addImage(pageImgData, 'PNG', x, y, scaledWidth, destHeight);
        }
      } else {
        // 单页处理
        pdf.addImage(imgData, 'PNG', x, y, scaledWidth, scaledHeight);
      }

      // 生成文件名
      const timestamp = new Date().toISOString().slice(0, 19).replace(/[:-]/g, '');
      const fileName = `新闻影响分析报告_${timestamp}.pdf`;
      
      // 下载PDF
      pdf.save(fileName);
      
    } catch (error) {
      console.error('PDF导出失败:', error);
      
      // 提供更详细的错误信息
      let errorMessage = 'PDF导出失败';
      if (error instanceof Error) {
        if (error.message.includes('oklch')) {
          errorMessage = '颜色格式不兼容，正在尝试修复...';
        } else if (error.message.includes('canvas')) {
          errorMessage = '图片生成失败，请稍后重试';
        } else {
          errorMessage = `导出失败: ${error.message}`;
        }
      }
      
      // 这里可以添加错误提示到UI
      alert(errorMessage);
      
    } finally {
      // Ensure cleanup functions are called even if there's an error
      try {
        cleanupGlobalStyles();
      } catch (cleanupError) {
        console.warn('Failed to cleanup global styles:', cleanupError);
      }
      
      setIsExporting(false);
    }
  };

  const analyzeNews = async () => {
    setIsLoading(true);
    setError(null);
    setAnalysis(null);

    try {
      const response = await fetch('http://localhost:8000/news/impact-analysis', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          news_id: newsData.id,
          news_title: newsData.title,
          news_content: newsData.content,
          news_source: newsData.source,
          news_publish_time: newsData.publish_time,
        }),
      });

      const data = await response.json();

      if (data.success) {
        setAnalysis(data.analysis);
        setCached(data.cached || false);
      } else {
        setError(data.error || '分析失败');
      }
    } catch (err) {
      setError('网络错误，请稍后重试');
      console.error('分析新闻影响失败:', err);
    } finally {
      setIsLoading(false);
    }
  };

  const getImpactLevelColor = (level: string) => {
    switch (level) {
      case '高':
        return 'text-red-600 bg-red-50 border-red-200';
      case '中':
        return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      case '低':
        return 'text-green-600 bg-green-50 border-green-200';
      default:
        return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  const getDirectionColor = (direction: string) => {
    switch (direction) {
      case '利多':
        return 'text-green-600 bg-green-50';
      case '利空':
        return 'text-red-600 bg-red-50';
      case '中性':
        return 'text-gray-600 bg-gray-50';
      default:
        return 'text-gray-600 bg-gray-50';
    }
  };

  const renderStockRecommendations = (stocks: Array<{
    symbol: string;
    name: string;
    impact: string;
    reason: string;
  }>) => {
    if (!stocks || stocks.length === 0) return <p className="text-gray-500">暂无推荐股票</p>;

    return (
      <div className="space-y-2">
        {stocks.map((stock, index) => (
          <div key={index} className="p-3 bg-gray-50 rounded-lg">
            <div className="flex items-center justify-between mb-1">
              <span className="font-medium text-gray-900">
                {stock.symbol} - {stock.name}
              </span>
              <span className={`px-2 py-1 rounded text-xs font-medium ${getDirectionColor(stock.impact)}`}>
                {stock.impact}
              </span>
            </div>
            <p className="text-sm text-gray-600">{stock.reason}</p>
          </div>
        ))}
      </div>
    );
  };

  const renderMarketSection = (title: string, marketData: any) => (
    <div className="bg-white rounded-lg border border-gray-200 p-4">
      <h3 className="text-lg font-semibold text-gray-900 mb-3">{title}</h3>
      
      <div className="grid grid-cols-2 gap-4 mb-4">
        <div>
          <span className="text-sm text-gray-500">影响程度</span>
          <div className={`mt-1 px-3 py-1 rounded-full text-sm font-medium border ${getImpactLevelColor(marketData.impact_level)}`}>
            {marketData.impact_level}
          </div>
        </div>
        <div>
          <span className="text-sm text-gray-500">影响方向</span>
          <div className={`mt-1 px-3 py-1 rounded-full text-sm font-medium ${getDirectionColor(marketData.direction)}`}>
            {marketData.direction}
          </div>
        </div>
      </div>

      {marketData.affected_sectors && marketData.affected_sectors.length > 0 && (
        <div className="mb-4">
          <h4 className="text-sm font-medium text-gray-700 mb-2">受影响行业</h4>
          <div className="flex flex-wrap gap-2">
            {marketData.affected_sectors.map((sector: string, index: number) => (
              <span key={index} className="px-2 py-1 bg-blue-50 text-blue-700 rounded text-xs">
                {sector}
              </span>
            ))}
          </div>
        </div>
      )}

      {marketData.recommended_stocks && (
        <div className="mb-4">
          <h4 className="text-sm font-medium text-gray-700 mb-2">推荐关注股票</h4>
          {renderStockRecommendations(marketData.recommended_stocks)}
        </div>
      )}

      {marketData.analysis && (
        <div>
          <h4 className="text-sm font-medium text-gray-700 mb-2">详细分析</h4>
          <p className="text-sm text-gray-600 leading-relaxed">{marketData.analysis}</p>
        </div>
      )}
    </div>
  );

  return (
    <div className={`news-impact-analysis ${className}`}>
      {/* CSS样式 - 控制PDF导出时的显示 */}
      <style jsx>{`
        .pdf-header {
          display: none;
        }
        
        @media print, (max-width: 0) {
          .pdf-header {
            display: block !important;
          }
        }
        
        /* PDF导出优化样式 */
        .news-impact-analysis {
          -webkit-print-color-adjust: exact;
          print-color-adjust: exact;
          color-adjust: exact;
        }
        
        .bg-white {
          background-color: #ffffff !important;
        }
        
        .bg-gray-50 {
          background-color: #f9fafb !important;
        }
        
        .bg-blue-50 {
          background-color: #eff6ff !important;
        }
        
        .bg-yellow-50 {
          background-color: #fefce8 !important;
        }
        
        .border {
          border-width: 1px !important;
        }
        
        .rounded-lg {
          border-radius: 0.5rem !important;
        }
      `}</style>
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 flex items-center">
            🤖 AI 市场影响分析
            {cached && (
              <span className="ml-2 px-2 py-1 bg-blue-50 text-blue-600 text-xs rounded">
                缓存结果
              </span>
            )}
          </h2>
          <p className="text-gray-600 mt-1">基于GLM-4-Flash的智能新闻影响分析</p>
        </div>
        <div className="flex items-center space-x-2">
          {/* Export PDF Button */}
          {analysis && (
            <Button
              onClick={handleExportPDF}
              disabled={isExporting}
              className="bg-blue-600 hover:bg-blue-700 text-white"
              data-export-button
            >
              {isExporting ? (
                <>
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
                  导出中...
                </>
              ) : (
                <>
                  <Download className="w-4 h-4 mr-2" />
                  导出PDF
                </>
              )}
            </Button>
          )}
          {onClose && (
            <button
              onClick={onClose}
              className="p-2 text-gray-400 hover:text-gray-600 transition-colors"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          )}
        </div>
      </div>

      {/* News Info */}
      <div className="bg-gray-50 rounded-lg p-4 mb-6">
        <h3 className="font-medium text-gray-900 mb-2">分析新闻</h3>
        <p className="text-sm text-gray-700 font-medium mb-1">{newsData.title}</p>
        <p className="text-xs text-gray-500">
          来源: {newsData.source || '未知'} | 时间: {newsData.publish_time || '未知'}
        </p>
      </div>

      {/* Loading State */}
      {isLoading && (
        <div className="flex items-center justify-center py-12">
          <div className="text-center">
            <LoadingSpinner size="lg" />
            <p className="text-gray-600 mt-4">AI正在分析新闻影响，请稍候...</p>
          </div>
        </div>
      )}

      {/* Error State */}
      {error && !isLoading && (
        <div className="mb-6">
          <ErrorDisplay 
            error={error}
            onRetry={analyzeNews}
          />
        </div>
      )}

      {/* Analysis Results */}
      {analysis && !isLoading && (
        <div id="news-analysis-content" className="space-y-6">
          {/* PDF Header - 只在PDF导出时显示 */}
          <div className="pdf-header text-center border-b border-gray-200 pb-6 mb-6" style={{ pageBreakAfter: 'avoid' }}>
            <h1 className="text-3xl font-bold text-gray-900 mb-2">金融新闻影响分析报告</h1>
            <p className="text-gray-600 mb-4">生成时间: {new Date().toLocaleString('zh-CN')}</p>
            
            {/* 新闻信息 */}
            <div className="bg-gray-50 rounded-lg p-4 text-left max-w-4xl mx-auto">
              <h3 className="font-medium text-gray-900 mb-2">分析新闻</h3>
              <p className="text-sm text-gray-700 font-medium mb-1">{newsData.title}</p>
              <p className="text-xs text-gray-500">
                来源: {newsData.source || '未知'} | 时间: {newsData.publish_time || '未知'}
              </p>
            </div>
          </div>

          {/* Overall Impact */}
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <h3 className="text-xl font-semibold text-gray-900 mb-4">📊 整体影响评估</h3>
            <div className="flex items-center mb-3">
              <span className="text-sm text-gray-500 mr-3">影响级别:</span>
              <span className={`px-4 py-2 rounded-full text-sm font-medium border ${getImpactLevelColor(analysis.overall_impact.level)}`}>
                {analysis.overall_impact.level}
              </span>
            </div>
            <p className="text-gray-700 leading-relaxed">{analysis.overall_impact.summary}</p>
          </div>

          {/* Market Analysis */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {renderMarketSection('🇺🇸 美股市场', analysis.us_market)}
            {renderMarketSection('🇨🇳 A股市场', analysis.a_share_market)}
            {renderMarketSection('🇭🇰 港股市场', analysis.hk_market)}
          </div>

          {/* Major Indices */}
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <h3 className="text-xl font-semibold text-gray-900 mb-4">📈 主要指数影响</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
              {Object.entries(analysis.major_indices).map(([key, data]) => {
                const indexNames: { [key: string]: string } = {
                  sp500: '标普500',
                  nasdaq: '纳斯达克',
                  shanghai_composite: '上证指数',
                  shenzhen_component: '深证成指',
                  hang_seng: '恒生指数'
                };
                
                return (
                  <div key={key} className="p-3 bg-gray-50 rounded-lg">
                    <div className="text-sm font-medium text-gray-900 mb-1">
                      {indexNames[key]}
                    </div>
                    <div className={`text-xs px-2 py-1 rounded ${getDirectionColor(data.impact)} mb-2`}>
                      {data.impact}
                    </div>
                    <p className="text-xs text-gray-600">{data.reason}</p>
                  </div>
                );
              })}
            </div>
          </div>

          {/* Key Stocks */}
          {analysis.key_stocks && analysis.key_stocks.length > 0 && (
            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <h3 className="text-xl font-semibold text-gray-900 mb-4">🎯 重点关注个股</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {analysis.key_stocks.map((stock, index) => (
                  <div key={index} className="p-4 bg-gray-50 rounded-lg">
                    <div className="flex items-center justify-between mb-2">
                      <div>
                        <span className="font-medium text-gray-900">{stock.symbol}</span>
                        <span className="text-sm text-gray-500 ml-2">({stock.market})</span>
                      </div>
                      <div className="flex space-x-2">
                        <span className={`px-2 py-1 rounded text-xs ${getDirectionColor(stock.impact)}`}>
                          {stock.impact}
                        </span>
                        <span className={`px-2 py-1 rounded text-xs border ${getImpactLevelColor(stock.impact_level)}`}>
                          {stock.impact_level}
                        </span>
                      </div>
                    </div>
                    <p className="text-sm text-gray-900 font-medium mb-1">{stock.name}</p>
                    <p className="text-sm text-gray-600">{stock.reason}</p>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Risk Assessment */}
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <h3 className="text-xl font-semibold text-gray-900 mb-4">⚠️ 风险评估</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <div className="mb-4">
                  <span className="text-sm text-gray-500">短期风险:</span>
                  <span className={`ml-2 px-3 py-1 rounded text-sm font-medium border ${getImpactLevelColor(analysis.risk_assessment.short_term_risk)}`}>
                    {analysis.risk_assessment.short_term_risk}
                  </span>
                </div>
                <div>
                  <span className="text-sm text-gray-500">中期风险:</span>
                  <span className={`ml-2 px-3 py-1 rounded text-sm font-medium border ${getImpactLevelColor(analysis.risk_assessment.medium_term_risk)}`}>
                    {analysis.risk_assessment.medium_term_risk}
                  </span>
                </div>
              </div>
              <div>
                <h4 className="text-sm font-medium text-gray-700 mb-2">关键风险因素</h4>
                <ul className="space-y-1">
                  {analysis.risk_assessment.key_risk_factors.map((factor, index) => (
                    <li key={index} className="text-sm text-gray-600 flex items-start">
                      <span className="text-red-500 mr-2">•</span>
                      {factor}
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          </div>

          {/* Investment Advice */}
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <h3 className="text-xl font-semibold text-gray-900 mb-4">💡 投资建议</h3>
            <div className="space-y-4">
              <div>
                <h4 className="text-sm font-medium text-gray-700 mb-2">投资策略</h4>
                <p className="text-sm text-gray-600 leading-relaxed">{analysis.investment_advice.strategy}</p>
              </div>
              
              <div>
                <h4 className="text-sm font-medium text-gray-700 mb-2">注意事项</h4>
                <ul className="space-y-1">
                  {analysis.investment_advice.attention_points.map((point, index) => (
                    <li key={index} className="text-sm text-gray-600 flex items-start">
                      <span className="text-blue-500 mr-2">•</span>
                      {point}
                    </li>
                  ))}
                </ul>
              </div>
              
              <div>
                <span className="text-sm text-gray-500">建议时间范围:</span>
                <span className="ml-2 px-3 py-1 bg-blue-50 text-blue-700 rounded text-sm">
                  {analysis.investment_advice.time_horizon}
                </span>
              </div>
            </div>
          </div>

          {/* Disclaimer */}
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4" style={{ pageBreakInside: 'avoid' }}>
            <div className="flex items-start">
              <svg className="w-5 h-5 text-yellow-600 mt-0.5 mr-3" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
              </svg>
              <div>
                <h4 className="text-sm font-medium text-yellow-800">免责声明</h4>
                <p className="text-sm text-yellow-700 mt-1">
                  本分析结果由AI生成，仅供参考，不构成投资建议。投资有风险，决策需谨慎。
                </p>
              </div>
            </div>
          </div>
        </div>
      )}


    </div>
  );
};

export default NewsImpactAnalysis; 